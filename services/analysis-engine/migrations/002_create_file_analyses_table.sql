-- Migration: Create file_analyses table
-- Date: 2025-01-08
-- Description: Create file_analyses table for storing complete file-level analysis data
-- This enables storing detailed AST, symbols, metrics, and embeddings for each analyzed file

-- Forward migration: Create file_analyses table

-- Create file_analyses table for storing complete file-level analysis data
-- Note: This table is interleaved with analyses for optimal performance
CREATE TABLE file_analyses (
    analysis_id STRING(36) NOT NULL,
    file_id STRING(36) NOT NULL,
    file_path STRING(2048) NOT NULL,
    language STRING(50),
    content_hash STRING(64), -- SHA-256 hash of file content for change detection
    size_bytes INT64,
    lines_of_code INT64,
    comment_lines INT64,
    blank_lines INT64,
    total_lines INT64,
    complexity_score FLOAT64,
    
    -- AST and structural data
    ast_data JSON, -- Complete AST structure
    symbols JSON, -- Extracted symbols (functions, classes, variables)
    dependencies JSON, -- Import/require statements and dependencies
    exports JSON, -- Export statements and public API
    
    -- Code organization
    functions JSON, -- Function definitions and metadata
    classes JSON, -- Class definitions and metadata
    interfaces JSON, -- Interface definitions (for languages that support them)
    
    -- Analysis results
    patterns JSON, -- Detected patterns in this file
    code_chunks JSON, -- Semantic code chunks for embeddings
    metrics JSON, -- File-level metrics (cyclomatic complexity, etc.)
    
    -- ML and embeddings
    embeddings_id STRING(36), -- Reference to stored embeddings in Cloud Storage
    embedding_metadata JSON, -- Metadata about embeddings (model, dimensions, etc.)
    
    -- Error handling
    parse_errors JSON, -- Any parsing errors encountered
    warnings JSON, -- File-specific warnings
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
    
    -- Foreign key relationships
    FOREIGN KEY (analysis_id) REFERENCES analyses (analysis_id),
) PRIMARY KEY (analysis_id, file_id),
  INTERLEAVE IN PARENT analyses ON DELETE CASCADE;

-- Create indexes for optimal query performance

-- Index for file path lookups within an analysis
CREATE INDEX idx_file_analyses_path ON file_analyses(analysis_id, file_path);

-- Index for language-based queries
CREATE INDEX idx_file_analyses_language ON file_analyses(language) STORING (lines_of_code, complexity_score);

-- Index for complexity analysis
CREATE INDEX idx_file_analyses_complexity ON file_analyses(complexity_score DESC) WHERE complexity_score IS NOT NULL;

-- Index for file size analysis
CREATE INDEX idx_file_analyses_size ON file_analyses(size_bytes DESC) WHERE size_bytes IS NOT NULL;

-- Index for content hash lookups (for change detection)
CREATE INDEX idx_file_analyses_content_hash ON file_analyses(content_hash) WHERE content_hash IS NOT NULL;

-- Index for embeddings lookups
CREATE INDEX idx_file_analyses_embeddings ON file_analyses(embeddings_id) WHERE embeddings_id IS NOT NULL;

-- Index for files with errors (for debugging and monitoring)
CREATE INDEX idx_file_analyses_errors ON file_analyses(analysis_id) WHERE parse_errors IS NOT NULL;

-- Index for timestamp-based queries
CREATE INDEX idx_file_analyses_created ON file_analyses(created_at DESC);

-- Rollback instructions (for reference only - do not uncomment in production)
-- These commands would reverse the migration if needed:
--
-- DROP INDEX idx_file_analyses_created;
-- DROP INDEX idx_file_analyses_errors;
-- DROP INDEX idx_file_analyses_embeddings;
-- DROP INDEX idx_file_analyses_content_hash;
-- DROP INDEX idx_file_analyses_size;
-- DROP INDEX idx_file_analyses_complexity;
-- DROP INDEX idx_file_analyses_language;
-- DROP INDEX idx_file_analyses_path;
-- DROP TABLE file_analyses;
