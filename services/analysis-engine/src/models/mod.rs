use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::fmt;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisRequest {
    pub repository_url: String,
    pub branch: Option<String>,
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub enable_patterns: bool,
    pub enable_embeddings: bool,
    pub webhook_url: Option<String>,
    pub languages: Vec<String>,
}

impl Default for AnalysisRequest {
    fn default() -> Self {
        Self {
            repository_url: String::new(),
            branch: None,
            include_patterns: vec![],
            exclude_patterns: vec![],
            enable_patterns: true,
            enable_embeddings: true,
            webhook_url: None,
            languages: vec![],
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ListAnalysesParams {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub status: Option<AnalysisStatus>,
    pub repository_url: Option<String>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisStatusResponse {
    pub analysis_id: String,
    pub status: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisResult {
    pub id: String,
    pub repository_url: String,
    pub branch: String,
    pub commit_hash: Option<String>,
    pub repository_size_bytes: Option<u64>,
    pub clone_time_ms: Option<u64>,
    pub status: AnalysisStatus,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub duration_seconds: Option<u64>,
    pub progress: Option<f64>,
    pub current_stage: Option<String>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub metrics: Option<RepositoryMetrics>,
    pub patterns: Vec<DetectedPattern>,
    pub languages: HashMap<String, LanguageStats>,
    pub embeddings: Option<Vec<CodeEmbedding>>,
    pub error_message: Option<String>,
    pub failed_files: Vec<FailedFile>,
    pub successful_analyses: Option<Vec<FileAnalysis>>,
    pub user_id: String,
    pub webhook_url: Option<String>,
    pub file_count: usize,
    pub success_rate: f64,
    pub performance_metrics: Option<PerformanceMetrics>,
    pub warnings: Vec<AnalysisWarning>,
}

#[derive(Debug, Copy, Clone, PartialEq, Deserialize, Serialize, Default)]
pub enum AnalysisStatus {
    #[default]
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled,
}

// Aligned with ast-output-v1.json schema RepositoryMetrics definition
#[derive(Debug, Clone, Deserialize, Serialize, Default)]
pub struct RepositoryMetrics {
    pub total_files: u32,
    pub total_lines: u32,
    pub total_complexity: u32,
    pub average_complexity: Option<f64>,
    pub maintainability_score: Option<f64>,
    pub technical_debt_minutes: Option<u32>,
    pub test_coverage_estimate: Option<f64>,
}

// Aligned with ast-output-v1.json schema PreDetectedPattern definition
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DetectedPattern {
    pub pattern_id: String,
    pub pattern_type: PatternType,
    pub confidence: f64,
    pub location: PatternLocation,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum PatternType {
    DesignPattern,
    AntiPattern,
    SecurityIssue,
    PerformanceIssue,
    CodeSmell,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PatternLocation {
    pub file_path: String,
    pub range: Range,
}

// Aligned with ast-output-v1.json schema
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LanguageStats {
    pub lines: usize,
    pub files: usize,
    pub percentage: f64,
    pub bytes: Option<usize>,
}

// Aligned with ast-output-v1.json schema CodeEmbedding definition
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CodeEmbedding {
    pub chunk_id: String,
    pub vector: Vec<f32>,
    pub model: String,
    pub metadata: Option<EmbeddingMetadata>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct EmbeddingMetadata {
    pub tokens_used: Option<u32>,
    pub created_at: Option<DateTime<Utc>>,
}

// Code chunk as per contract schema
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CodeChunk {
    pub chunk_id: String,
    pub content: String,
    pub range: Range,
    #[serde(rename = "type")]
    pub chunk_type: ChunkType,
    pub language: Option<String>,
    pub context: Option<ChunkContext>,
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ChunkType {
    Function,
    Class,
    Method,
    Block,
    Comment,
    Import,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ChunkContext {
    pub parent_symbol: Option<String>,
    pub imports: Option<Vec<String>>,
    pub dependencies: Option<Vec<String>>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FailedFile {
    pub file_path: String,
    pub error_message: String,
    pub error_type: FileErrorType,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum FileErrorType {
    ParseError,
    UnsupportedLanguage,
    FileTooLarge,
    Timeout,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProgressUpdate {
    pub analysis_id: String,
    pub progress: f64,
    pub stage: String,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub files_processed: Option<usize>,
    pub total_files: Option<usize>,
}

// Aligned with ast-output-v1.json schema FileAnalysis definition
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FileAnalysis {
    pub path: String,
    pub language: String,
    pub content_hash: String,
    pub size_bytes: Option<u64>,
    pub ast: AstNode,
    pub metrics: FileMetrics,
    pub chunks: Option<Vec<CodeChunk>>,
    pub symbols: Option<Vec<Symbol>>,
}

// Aligned with ast-output-v1.json schema ASTNode definition
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AstNode {
    #[serde(rename = "type")]
    pub node_type: String,
    pub name: Option<String>,
    pub range: Range,
    pub children: Vec<AstNode>,
    pub properties: Option<serde_json::Value>,
    pub text: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Range {
    pub start: Position,
    pub end: Position,
}

// Aligned with ast-output-v1.json schema Position definition
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Position {
    pub line: u32,  // 0-based as per contract
    pub column: u32, // 0-based as per contract
    pub byte: u32,   // byte offset from file start
}

// Aligned with ast-output-v1.json schema Symbol definition
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Symbol {
    pub name: String,
    #[serde(rename = "type")]
    pub symbol_type: SymbolType,
    pub range: Range,
    pub visibility: Option<SymbolVisibility>,
    pub signature: Option<String>,
    pub documentation: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum SymbolVisibility {
    Public,
    Private,
    Protected,
    Internal,
}

// Aligned with ast-output-v1.json schema Symbol type enum
#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum SymbolType {
    Function,
    Method,
    Class,
    Interface,
    Variable,
    Constant,
    Type,
    Namespace,
}

// Aligned with ast-output-v1.json schema FileMetrics definition
#[derive(Debug, Clone, Deserialize, Serialize, Default)]
pub struct FileMetrics {
    pub lines_of_code: u32,
    pub total_lines: Option<u32>,
    pub complexity: u32,
    pub maintainability_index: f64,
    pub function_count: Option<u32>,
    pub class_count: Option<u32>,
    pub comment_ratio: Option<f64>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ParseError {
    pub file_path: String,
    pub error_type: ParseErrorType,
    pub message: String,
    pub position: Option<Position>,
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
pub enum ParseErrorType {
    ParseError,
    SyntaxError,
    UnsupportedLanguage,
    FileTooLarge,
    Timeout,
    Other,
}

#[derive(Debug, Clone, Deserialize, Serialize, Default)]
pub struct PerformanceMetrics {
    pub total_processing_ms: u64,
    pub clone_duration_ms: u64,
    pub language_detection_ms: u64,
    pub file_collection_ms: u64,
    pub ast_parsing_ms: u64,
    pub metrics_calculation_ms: u64,
    pub pattern_detection_ms: u64,
    pub embedding_generation_ms: u64,
    pub cleanup_ms: u64,
    pub files_analyzed: u64,
    pub successful_parses: u64,
    pub failed_parses: u64,
    pub patterns_detected: u64,
    pub embeddings_generated: u64,
    pub memory_peak_mb: u64,
    pub cpu_usage_percent: f32,
}

// Additional structs for API responses
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisResponse {
    pub analysis_id: String,
    pub status: AnalysisStatus,
    pub created_at: DateTime<Utc>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub repository: RepositoryInfo,
    pub webhook_url: Option<String>,
    pub progress_url: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RepositoryInfo {
    pub url: String,
    pub branch: String,
    pub commit_sha: Option<String>,
    pub size_bytes: Option<u64>,
}

// Implementations
impl AnalysisRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.repository_url.is_empty() {
            return Err("Repository URL is required".to_string());
        }
        if !self.repository_url.starts_with("http://") && !self.repository_url.starts_with("https://") {
            return Err("Repository URL must be a valid HTTP(S) URL".to_string());
        }
        Ok(())
    }
}

impl fmt::Display for AnalysisStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AnalysisStatus::Pending => write!(f, "pending"),
            AnalysisStatus::InProgress => write!(f, "in_progress"),
            AnalysisStatus::Completed => write!(f, "completed"),
            AnalysisStatus::Failed => write!(f, "failed"),
            AnalysisStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

impl fmt::Display for SymbolType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SymbolType::Function => write!(f, "function"),
            SymbolType::Method => write!(f, "method"),
            SymbolType::Class => write!(f, "class"),
            SymbolType::Interface => write!(f, "interface"),
            SymbolType::Variable => write!(f, "variable"),
            SymbolType::Constant => write!(f, "constant"),
            SymbolType::Type => write!(f, "type"),
            SymbolType::Namespace => write!(f, "namespace"),
        }
    }
}

impl Default for AnalysisResult {
    fn default() -> Self {
        Self {
            id: String::new(),
            repository_url: String::new(),
            branch: String::new(),
            commit_hash: None,
            repository_size_bytes: None,
            clone_time_ms: None,
            status: AnalysisStatus::default(),
            started_at: Utc::now(),
            completed_at: None,
            duration_seconds: None,
            progress: None,
            current_stage: None,
            estimated_completion: None,
            metrics: None,
            patterns: Vec::new(),
            languages: HashMap::new(),
            embeddings: None,
            error_message: None,
            failed_files: Vec::new(),
            successful_analyses: None,
            user_id: String::new(),
            webhook_url: None,
            file_count: 0,
            success_rate: 0.0,
            performance_metrics: None,
            warnings: Vec::new(),
        }
    }
}

impl From<ParseErrorType> for FileErrorType {
    fn from(error_type: ParseErrorType) -> Self {
        match error_type {
            ParseErrorType::ParseError => FileErrorType::ParseError,
            ParseErrorType::SyntaxError => FileErrorType::ParseError,
            ParseErrorType::UnsupportedLanguage => FileErrorType::UnsupportedLanguage,
            ParseErrorType::FileTooLarge => FileErrorType::FileTooLarge,
            ParseErrorType::Timeout => FileErrorType::Timeout,
            ParseErrorType::Other => FileErrorType::ParseError,
        }
    }
}

#[derive(Debug, Clone, Copy, Deserialize, Serialize)]
pub enum WarningType {
    ParseError,
    UnsupportedSyntax,
    LargeFile,
    MemoryLimit,
    TimeoutRisk,
    EmbeddingFailure,
    GitError,
    StorageError,
    NetworkError,
    ConfigurationError,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisWarning {
    pub warning_type: WarningType,
    pub code: String,
    pub message: String,
    pub file_path: Option<String>,
    pub line_number: Option<u32>,
    pub severity: WarningSeverity,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub context: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum WarningSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl AnalysisWarning {
    /// Create a new warning with current timestamp
    pub fn new(
        warning_type: WarningType,
        message: String,
        severity: WarningSeverity,
    ) -> Self {
        Self {
            warning_type,
            code: Self::generate_code(&warning_type),
            message,
            file_path: None,
            line_number: None,
            severity,
            timestamp: chrono::Utc::now(),
            context: None,
        }
    }

    /// Create a warning with file context
    pub fn with_file_context(
        warning_type: WarningType,
        message: String,
        severity: WarningSeverity,
        file_path: String,
        line_number: Option<u32>,
    ) -> Self {
        Self {
            warning_type,
            code: Self::generate_code(&warning_type),
            message,
            file_path: Some(file_path),
            line_number,
            severity,
            timestamp: chrono::Utc::now(),
            context: None,
        }
    }

    /// Add context data to the warning
    pub fn with_context(mut self, context: serde_json::Value) -> Self {
        self.context = Some(context);
        self
    }

    /// Generate a warning code based on the type
    fn generate_code(warning_type: &WarningType) -> String {
        match warning_type {
            WarningType::ParseError => "PARSE_001".to_string(),
            WarningType::UnsupportedSyntax => "PARSE_002".to_string(),
            WarningType::LargeFile => "FILE_001".to_string(),
            WarningType::MemoryLimit => "MEMORY_001".to_string(),
            WarningType::TimeoutRisk => "TIMEOUT_001".to_string(),
            WarningType::EmbeddingFailure => "EMBED_001".to_string(),
            WarningType::GitError => "GIT_001".to_string(),
            WarningType::StorageError => "STORAGE_001".to_string(),
            WarningType::NetworkError => "NETWORK_001".to_string(),
            WarningType::ConfigurationError => "CONFIG_001".to_string(),
        }
    }
}
