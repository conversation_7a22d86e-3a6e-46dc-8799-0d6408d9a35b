# Query Intelligence Service

Natural language query processing service for the CCL (Codebase Context Layer) platform. This service enables developers to ask questions about their codebase in plain English and receive intelligent, context-aware responses.

## Overview

The Query Intelligence service is a Python-based microservice that:
- Processes natural language queries about code
- Performs semantic search across code embeddings
- Integrates with Vertex AI and Gemini 2.5 for understanding
- Generates contextual responses with code references
- Provides confidence scoring and reasoning
- Supports real-time streaming responses via WebSocket

## Architecture

### Technology Stack
- **Language**: Python 3.11+
- **Framework**: FastAPI (async web framework)
- **AI/ML**: Vertex AI, Gemini 2.5, Sentence Transformers
- **Vector Database**: Pinecone
- **Cache**: Redis (Memorystore)
- **Monitoring**: Prometheus, Structured Logging

### Service Boundaries
- **No direct database access** - Uses service APIs only
- **Stateless design** - All state in Redis/Pinecone
- **Event-driven** - Publishes/subscribes to platform events

## Features

### Core Capabilities
- **Natural Language Understanding**: Analyzes query intent and extracts code elements
- **Semantic Search**: Finds relevant code using vector embeddings
- **Intelligent Response Generation**: Creates helpful, context-aware answers
- **Code References**: Links responses to specific code locations
- **Confidence Scoring**: Provides reliability metrics for responses
- **Follow-up Questions**: Suggests related queries for deeper exploration

### Advanced Features
- **Streaming Responses**: Real-time response generation via WebSocket
- **Multi-turn Conversations**: Maintains context across queries
- **Caching**: Intelligent caching for improved performance
- **Rate Limiting**: Protects against abuse
- **Authentication**: JWT-based auth with role support

## API Endpoints

### REST API

#### Process Query
```http
POST /api/v1/query
Authorization: Bearer <token> (optional)

{
  "query": "How does the authentication middleware work?",
  "repository_id": "repo-123",
  "filters": {},
  "stream": false
}
```

Response:
```json
{
  "answer": "The authentication middleware uses JWT tokens...",
  "intent": "explain",
  "confidence": 0.92,
  "references": [
    {
      "file_path": "src/middleware/auth.py",
      "start_line": 45,
      "end_line": 67,
      "snippet": "...",
      "relevance_score": 0.95
    }
  ],
  "execution_time_ms": 89.5,
  "follow_up_questions": [
    "What JWT algorithm is used?",
    "How are tokens validated?",
    "What happens when a token expires?"
  ]
}
```

### WebSocket API

#### Streaming Query
```javascript
const ws = new WebSocket('ws://localhost:8002/api/v1/ws/query');

ws.send(JSON.stringify({
  query: "Explain the database schema",
  repository_id: "repo-123"
}));

// Receive streaming chunks
ws.onmessage = (event) => {
  const chunk = JSON.parse(event.data);
  switch(chunk.type) {
    case 'text':
      // Append text to response
      break;
    case 'reference':
      // Add code reference
      break;
    case 'done':
      // Query complete
      break;
  }
};
```

## Configuration

### Environment Variables

```bash
# Service Configuration
SERVICE_NAME=query-intelligence
PORT=8002
ENVIRONMENT=production
LOG_LEVEL=INFO

# External Services
REDIS_URL=redis://localhost:6379
ANALYSIS_ENGINE_URL=http://analysis-engine:8001

# GCP Configuration
GCP_PROJECT_ID=ccl-production
GCP_REGION=us-central1

# Vertex AI / Gemini
GEMINI_MODEL_NAME=gemini-2.0-flash-exp
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2

# Pinecone
PINECONE_API_KEY=your-api-key
PINECONE_INDEX_NAME=ccl-code-embeddings

# Security
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60
```

## Development

### Prerequisites
- Python 3.11+
- Poetry for dependency management
- Docker and Docker Compose
- Google Cloud SDK
- Redis (for local development)

### Local Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/episteme/ccl.git
   cd ccl/services/query-intelligence
   ```

2. **Install dependencies**
   ```bash
   poetry install
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run Redis locally**
   ```bash
   docker run -d -p 6379:6379 redis:7-alpine
   ```

5. **Start the service**
   ```bash
   poetry run uvicorn query_intelligence.main:app --reload --port 8002
   ```

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=query_intelligence --cov-report=html

# Run specific test file
poetry run pytest tests/unit/test_query_processor.py

# Run integration tests
poetry run pytest tests/integration/
```

### Code Quality

```bash
# Format code
poetry run black src/ tests/

# Lint code
poetry run ruff check src/ tests/

# Type checking
poetry run mypy src/
```

## Deployment

### Building the Container

```bash
# Build Docker image
docker build -t query-intelligence:latest .

# Tag for Artifact Registry
docker tag query-intelligence:latest gcr.io/ccl-production/query-intelligence:latest

# Push to registry
docker push gcr.io/ccl-production/query-intelligence:latest
```

### Deploying to Cloud Run

```bash
# Deploy to Cloud Run
gcloud run deploy query-intelligence \
  --image gcr.io/ccl-production/query-intelligence:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars="ENVIRONMENT=production" \
  --min-instances=1 \
  --max-instances=100 \
  --memory=8Gi \
  --cpu=4
```

## Performance

### SLOs
- **Response Time**: <100ms (p95)
- **Availability**: 99.95%
- **Error Rate**: <1%
- **Concurrent Queries**: 1000+ per second

### Optimization Strategies
1. **Caching**: Query results cached for 24 hours
2. **Connection Pooling**: Reused connections to external services
3. **Async Processing**: All I/O operations are async
4. **Vector Index**: Optimized Pinecone configuration
5. **Model Optimization**: Cached model instances

## Monitoring

### Metrics
Prometheus metrics available at `/metrics`:
- `query_intelligence_queries_total`: Total queries by intent and status
- `query_intelligence_query_duration_seconds`: Query processing duration
- `query_intelligence_llm_tokens_total`: LLM token usage
- `query_intelligence_cache_hits_total`: Cache hit rate

### Logging
Structured JSON logging with correlation IDs:
```json
{
  "timestamp": "2024-01-20T10:30:45Z",
  "level": "INFO",
  "msg": "query_processed",
  "query_id": "q-123",
  "intent": "explain",
  "execution_time_ms": 87.3,
  "confidence": 0.92
}
```

### Health Checks
- `/health`: Basic health status
- `/ready`: Readiness check including dependencies

## Troubleshooting

### Common Issues

1. **Slow Response Times**
   - Check Redis connection
   - Verify Pinecone index performance
   - Monitor Vertex AI rate limits

2. **Authentication Errors**
   - Verify JWT secret key
   - Check token expiration
   - Ensure proper headers

3. **Rate Limiting**
   - Monitor rate limit headers
   - Adjust limits if needed
   - Check Redis connectivity

4. **Vector Search Issues**
   - Verify Pinecone API key
   - Check embedding dimensions
   - Monitor index capacity

## Contributing

1. Follow the coding standards in PLANNING.md
2. Write tests for new features
3. Update documentation
4. Submit PR with clear description

## License

Proprietary - Episteme/CCL Platform