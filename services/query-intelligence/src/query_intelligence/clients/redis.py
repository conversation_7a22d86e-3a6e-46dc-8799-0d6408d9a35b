import redis.asyncio as redis
from functools import lru_cache
from ..config.settings import get_settings

settings = get_settings()


class RedisClient:
    def __init__(self):
        self.pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL, max_connections=10
        )
        self._client = None

    async def _get_client(self):
        """Get or create Redis client"""
        if not self._client:
            self._client = redis.Redis(connection_pool=self.pool, decode_responses=True)
        return self._client

    async def get(self, key: str) -> str:
        client = await self._get_client()
        return await client.get(key)

    async def set(self, key: str, value: str, ex: int = 3600) -> bool:
        client = await self._get_client()
        return await client.set(key, value, ex=ex)

    async def delete(self, key: str) -> int:
        client = await self._get_client()
        return await client.delete(key)

    async def exists(self, key: str) -> bool:
        client = await self._get_client()
        return await client.exists(key)

    async def ttl(self, key: str) -> int:
        client = await self._get_client()
        return await client.ttl(key)

    async def keys(self, pattern: str) -> list:
        client = await self._get_client()
        return await client.keys(pattern)

    async def ping(self) -> bool:
        """Test Redis connection"""
        client = await self._get_client()
        return await client.ping()

    async def aclose(self):
        """Async close for compatibility"""
        await self.close()

    def pipeline(self):
        """Get Redis pipeline for atomic operations"""
        return redis.Redis(connection_pool=self.pool, decode_responses=True).pipeline()

    async def close(self):
        if self._client:
            await self._client.close()
            self._client = None


# Create singleton instance
_redis_client_instance = None


@lru_cache()
def get_redis_client() -> RedisClient:
    """Get singleton Redis client instance"""
    global _redis_client_instance
    if _redis_client_instance is None:
        _redis_client_instance = RedisClient()
    return _redis_client_instance


# For backward compatibility
redis_client = get_redis_client()
