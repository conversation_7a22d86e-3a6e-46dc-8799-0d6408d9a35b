import asyncio
import time
from typing import List, Dict, Optional, Any

import numpy as np
import structlog
from sentence_transformers import SentenceTransformer
from pinecone import <PERSON>cone, ServerlessSpec

from ..models import EmbeddingVector, CodeChunk, SearchResult
from ..config.settings import get_settings
from ..clients.analysis_engine import get_analysis_engine_client

logger = structlog.get_logger()
settings = get_settings()


class SemanticSearchService:
    """Service for semantic code search using vector embeddings"""
    
    def __init__(self):
        # Initialize embedding model
        self.embedding_model = SentenceTransformer(
            settings.EMBEDDING_MODEL_NAME or "sentence-transformers/all-MiniLM-L6-v2"
        )
        
        # Initialize Pinecone client if configured
        self.pinecone_client = None
        self.index = None
        if settings.PINECONE_API_KEY:
            self._init_pinecone()
        
        # Cache for embeddings
        self._embedding_cache = {}
        
    def _init_pinecone(self):
        """Initialize Pinecone vector database"""
        try:
            self.pinecone_client = Pinecone(api_key=settings.PINECONE_API_KEY)
            
            # Create or get index
            index_name = settings.PINECONE_INDEX_NAME or "ccl-code-embeddings"
            
            if index_name not in self.pinecone_client.list_indexes().names():
                self.pinecone_client.create_index(
                    name=index_name,
                    dimension=self.embedding_model.get_sentence_embedding_dimension(),
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud=settings.PINECONE_CLOUD or "aws",
                        region=settings.PINECONE_REGION or "us-west-2"
                    )
                )
            
            self.index = self.pinecone_client.Index(index_name)
            logger.info("pinecone_initialized", index_name=index_name)
            
        except Exception as e:
            logger.error("pinecone_init_failed", error=str(e))
            self.pinecone_client = None
            self.index = None
    
    async def generate_embedding(self, text: str, context_type: str = "code") -> np.ndarray:
        """Generate embedding for text"""
        # Check cache
        cache_key = f"{context_type}:{hash(text)}"
        if cache_key in self._embedding_cache:
            return self._embedding_cache[cache_key]
        
        # Add context prefix for better embeddings
        if context_type == "code":
            text = f"[CODE] {text}"
        elif context_type == "query":
            text = f"[QUERY] {text}"
        
        # Generate embedding
        embedding = await asyncio.to_thread(
            self.embedding_model.encode,
            text,
            normalize_embeddings=True
        )
        
        # Cache the result
        self._embedding_cache[cache_key] = embedding
        
        return embedding
    
    async def search(
        self,
        embedding: np.ndarray,
        repository_id: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 20
    ) -> SearchResult:
        """Search for similar code chunks"""
        start_time = time.time()
        
        logger.info(
            "semantic_search_start",
            repository_id=repository_id,
            limit=limit,
            has_filters=bool(filters)
        )
        
        try:
            if self.index:
                # Use Pinecone for search
                chunks = await self._search_pinecone(
                    embedding, repository_id, filters, limit
                )
            else:
                # Fallback to analysis engine search
                chunks = await self._search_analysis_engine(
                    embedding, repository_id, filters, limit
                )
            
            search_time_ms = (time.time() - start_time) * 1000
            
            logger.info(
                "semantic_search_complete",
                repository_id=repository_id,
                results_found=len(chunks),
                search_time_ms=search_time_ms
            )
            
            return SearchResult(
                chunks=chunks,
                total_results=len(chunks),
                search_time_ms=search_time_ms,
                query_embedding=EmbeddingVector.from_numpy(embedding)
            )
            
        except Exception as e:
            logger.error(
                "semantic_search_failed",
                repository_id=repository_id,
                error=str(e),
                exc_info=True
            )
            # Return empty result on error
            return SearchResult(
                chunks=[],
                total_results=0,
                search_time_ms=(time.time() - start_time) * 1000
            )
    
    async def _search_pinecone(
        self,
        embedding: np.ndarray,
        repository_id: str,
        filters: Optional[Dict[str, Any]],
        limit: int
    ) -> List[CodeChunk]:
        """Search using Pinecone vector database"""
        # Build Pinecone filter
        pinecone_filter = {"repository_id": repository_id}
        if filters:
            pinecone_filter.update(filters)
        
        # Query Pinecone
        results = await asyncio.to_thread(
            self.index.query,
            vector=embedding.tolist(),
            filter=pinecone_filter,
            top_k=limit,
            include_metadata=True
        )
        
        # Convert results to CodeChunk objects
        chunks = []
        for match in results.matches:
            metadata = match.metadata
            chunk = CodeChunk(
                file_path=metadata.get("file_path", ""),
                start_line=metadata.get("start_line", 0),
                end_line=metadata.get("end_line", 0),
                content=metadata.get("content", ""),
                language=metadata.get("language", ""),
                similarity_score=match.score,
                recency_score=metadata.get("recency_score", 0.0),
                metadata=metadata
            )
            chunks.append(chunk)
        
        return chunks
    
    async def _search_analysis_engine(
        self,
        embedding: np.ndarray,
        repository_id: str,
        filters: Optional[Dict[str, Any]],
        limit: int
    ) -> List[CodeChunk]:
        """Search using analysis engine as fallback"""
        client = get_analysis_engine_client()
        
        # Call analysis engine search endpoint
        response = await client.search_embeddings(
            repository_id=repository_id,
            query_embedding=embedding.tolist(),
            limit=limit,
            filters=filters
        )
        
        # Convert response to CodeChunk objects
        chunks = []
        for item in response.get("results", []):
            chunk = CodeChunk(
                file_path=item.get("file_path", ""),
                start_line=item.get("start_line", 0),
                end_line=item.get("end_line", 0),
                content=item.get("content", ""),
                language=item.get("language", ""),
                similarity_score=item.get("similarity_score", 0.0),
                recency_score=item.get("recency_score", 0.0),
                metadata=item.get("metadata", {})
            )
            chunks.append(chunk)
        
        return chunks
    
    async def index_code_chunk(
        self,
        chunk: CodeChunk,
        repository_id: str
    ) -> bool:
        """Index a code chunk for future search"""
        if not self.index:
            logger.warning("indexing_skipped_no_pinecone")
            return False
        
        try:
            # Generate embedding for chunk
            embedding = await self.generate_embedding(
                chunk.content,
                context_type="code"
            )
            
            # Prepare metadata
            metadata = {
                "repository_id": repository_id,
                "file_path": chunk.file_path,
                "start_line": chunk.start_line,
                "end_line": chunk.end_line,
                "content": chunk.content[:1000],  # Truncate for metadata limits
                "language": chunk.language,
                "recency_score": chunk.recency_score,
                **chunk.metadata
            }
            
            # Create unique ID
            chunk_id = f"{repository_id}:{chunk.file_path}:{chunk.start_line}"
            
            # Upsert to Pinecone
            await asyncio.to_thread(
                self.index.upsert,
                vectors=[(chunk_id, embedding.tolist(), metadata)]
            )
            
            logger.info(
                "code_chunk_indexed",
                repository_id=repository_id,
                chunk_id=chunk_id
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "code_chunk_indexing_failed",
                repository_id=repository_id,
                error=str(e),
                exc_info=True
            )
            return False
    
    async def batch_index_chunks(
        self,
        chunks: List[CodeChunk],
        repository_id: str,
        batch_size: int = 100
    ) -> int:
        """Batch index multiple code chunks"""
        if not self.index:
            logger.warning("batch_indexing_skipped_no_pinecone")
            return 0
        
        indexed_count = 0
        
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            vectors = []
            
            for chunk in batch:
                try:
                    # Generate embedding
                    embedding = await self.generate_embedding(
                        chunk.content,
                        context_type="code"
                    )
                    
                    # Prepare metadata
                    metadata = {
                        "repository_id": repository_id,
                        "file_path": chunk.file_path,
                        "start_line": chunk.start_line,
                        "end_line": chunk.end_line,
                        "content": chunk.content[:1000],
                        "language": chunk.language,
                        "recency_score": chunk.recency_score,
                        **chunk.metadata
                    }
                    
                    # Create unique ID
                    chunk_id = f"{repository_id}:{chunk.file_path}:{chunk.start_line}"
                    
                    vectors.append((chunk_id, embedding.tolist(), metadata))
                    
                except Exception as e:
                    logger.error(
                        "chunk_embedding_failed",
                        error=str(e),
                        chunk=f"{chunk.file_path}:{chunk.start_line}"
                    )
                    continue
            
            if vectors:
                try:
                    await asyncio.to_thread(
                        self.index.upsert,
                        vectors=vectors
                    )
                    indexed_count += len(vectors)
                    
                    logger.info(
                        "batch_indexed",
                        repository_id=repository_id,
                        batch_size=len(vectors),
                        total_indexed=indexed_count
                    )
                    
                except Exception as e:
                    logger.error(
                        "batch_indexing_failed",
                        error=str(e),
                        batch_size=len(vectors)
                    )
        
        return indexed_count