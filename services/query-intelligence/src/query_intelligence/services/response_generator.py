from typing import List, Dict, Optional, Any
import structlog

from ..models import (
    CodeChunk,
    QueryIntent,
    QueryContext,
    GeneratedResponse,
)

logger = structlog.get_logger()


class ResponseGenerator:
    """Service for generating and formatting query responses"""
    
    def format_response_with_references(
        self,
        response: GeneratedResponse,
        references: List[Dict[str, Any]]
    ) -> str:
        """Format response with code references"""
        formatted_parts = [response.text]
        
        if references:
            formatted_parts.append("\n\n---\n**Code References:**")
            for i, ref in enumerate(references, 1):
                formatted_parts.append(
                    f"\n{i}. `{ref['file_path']}:{ref['start_line']}-{ref['end_line']}` "
                    f"(relevance: {ref['relevance_score']:.2%})"
                )
        
        return "\n".join(formatted_parts)
    
    def generate_error_response(
        self,
        error_type: str,
        query: str,
        details: Optional[str] = None
    ) -> str:
        """Generate user-friendly error response"""
        error_messages = {
            "no_results": f"I couldn't find any relevant code for your query: '{query}'. "
                         "Try rephrasing your question or being more specific about what you're looking for.",
            
            "analysis_error": "I encountered an error while analyzing the codebase. "
                             "Please try again or contact support if the issue persists.",
            
            "timeout": "The query took too long to process. "
                      "Try breaking it down into smaller, more specific questions.",
            
            "rate_limit": "You've reached the query rate limit. "
                         "Please wait a moment before trying again.",
            
            "invalid_query": f"I couldn't understand your query: '{query}'. "
                            "Please rephrase it or provide more context.",
        }
        
        base_message = error_messages.get(error_type, "An unexpected error occurred.")
        
        if details:
            return f"{base_message}\n\nDetails: {details}"
        
        return base_message
    
    def generate_clarification_request(
        self,
        query: str,
        ambiguity_type: str,
        suggestions: List[str]
    ) -> str:
        """Generate response requesting clarification"""
        clarification_templates = {
            "multiple_matches": f"I found multiple possibilities for '{query}'. Could you be more specific?",
            "vague_intent": f"I'm not sure what you'd like to know about '{query}'. Are you looking to:",
            "missing_context": f"To better help you with '{query}', I need more context:",
        }
        
        response_parts = [clarification_templates.get(ambiguity_type, f"Could you clarify your query: '{query}'?")]
        
        if suggestions:
            response_parts.append("\n")
            for i, suggestion in enumerate(suggestions[:5], 1):
                response_parts.append(f"{i}. {suggestion}")
        
        return "\n".join(response_parts)
    
    def enhance_response_with_examples(
        self,
        response: str,
        code_chunks: List[CodeChunk],
        intent: QueryIntent
    ) -> str:
        """Enhance response with relevant code examples"""
        if not code_chunks or intent not in [QueryIntent.EXPLAIN, QueryIntent.FIND]:
            return response
        
        # Find the most relevant example
        best_chunk = max(code_chunks, key=lambda c: c.combined_score)
        
        if best_chunk.combined_score > 0.8:
            example_text = f"\n\n**Example from your codebase:**\n```{best_chunk.language}\n{best_chunk.content[:300]}"
            if len(best_chunk.content) > 300:
                example_text += "\n..."
            example_text += f"\n```\n*From: {best_chunk.file_path}:{best_chunk.start_line}*"
            
            return response + example_text
        
        return response
    
    def generate_summary_card(
        self,
        query: str,
        answer_preview: str,
        intent: QueryIntent,
        confidence: float,
        reference_count: int
    ) -> Dict[str, Any]:
        """Generate a summary card for the query result"""
        return {
            "query": query,
            "answer_preview": answer_preview[:200] + "..." if len(answer_preview) > 200 else answer_preview,
            "intent": intent.value,
            "confidence": confidence,
            "reference_count": reference_count,
            "confidence_label": self._get_confidence_label(confidence),
            "intent_icon": self._get_intent_icon(intent),
        }
    
    def _get_confidence_label(self, confidence: float) -> str:
        """Get human-readable confidence label"""
        if confidence >= 0.9:
            return "Very High"
        elif confidence >= 0.75:
            return "High"
        elif confidence >= 0.5:
            return "Medium"
        else:
            return "Low"
    
    def _get_intent_icon(self, intent: QueryIntent) -> str:
        """Get icon for intent type"""
        intent_icons = {
            QueryIntent.EXPLAIN: "📚",
            QueryIntent.FIND: "🔍",
            QueryIntent.DEBUG: "🐛",
            QueryIntent.REFACTOR: "♻️",
            QueryIntent.ANALYZE: "📊",
            QueryIntent.COMPARE: "⚖️",
            QueryIntent.UNKNOWN: "❓",
        }
        return intent_icons.get(intent, "💬")