import asyncio
import json
import time
from typing import List, Dict, Optional, Any

import structlog
from google.cloud import aiplatform
from google.auth import default
from google.api_core import retry
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig, Content, Part

from ..models import (
    QueryContext,
    IntentAnalysis,
    CodeChunk,
    GeneratedResponse,
)
from ..config.settings import get_settings

logger = structlog.get_logger()
settings = get_settings()


class LLMService:
    """Service for LLM interactions using Vertex AI and Gemini"""
    
    def __init__(self):
        # Initialize Vertex AI
        credentials, project = default()
        vertexai.init(
            project=settings.GCP_PROJECT_ID or project,
            location=settings.GCP_REGION or "us-central1",
            credentials=credentials
        )
        
        # Initialize models
        self.gemini_model = GenerativeModel(
            settings.GEMINI_MODEL_NAME or "gemini-2.0-pro"
        )
        
        # Generation configs
        self.default_config = GenerationConfig(
            temperature=0.3,
            max_output_tokens=2048,
            top_p=0.95,
            top_k=40,
        )
        
        self.json_config = GenerationConfig(
            temperature=0.1,
            max_output_tokens=1024,
            response_mime_type="application/json",
        )
        
        # Retry configuration
        self.retry_config = retry.Retry(
            initial=1.0,
            maximum=60.0,
            multiplier=2.0,
            predicate=retry.if_exception_type(Exception),
        )
    
    async def generate_response(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext
    ) -> GeneratedResponse:
        """Generate response based on query and code context"""
        start_time = time.time()
        
        logger.info(
            "llm_response_generation_start",
            query_length=len(query),
            chunk_count=len(code_chunks),
            intent=intent.primary_intent.value
        )
        
        try:
            # Build prompt
            prompt = self._build_response_prompt(query, intent, code_chunks, context)
            
            # Generate response with retry
            response = await asyncio.to_thread(
                self._generate_with_retry,
                prompt,
                self.default_config
            )
            
            generation_time_ms = (time.time() - start_time) * 1000
            
            # Calculate confidence
            confidence = self._calculate_confidence(response, code_chunks, intent)
            
            logger.info(
                "llm_response_generated",
                generation_time_ms=generation_time_ms,
                response_length=len(response.text),
                confidence=confidence
            )
            
            return GeneratedResponse(
                text=response.text,
                confidence=confidence,
                model_used=settings.GEMINI_MODEL_NAME or "gemini-2.0-pro",
                prompt_tokens=response.usage_metadata.prompt_token_count,
                completion_tokens=response.usage_metadata.candidates_token_count,
                generation_time_ms=generation_time_ms
            )
            
        except Exception as e:
            logger.error(
                "llm_response_generation_failed",
                error=str(e),
                exc_info=True
            )
            raise
    
    async def generate_json_response(self, prompt: str) -> Dict[str, Any]:
        """Generate a JSON response from the LLM"""
        try:
            response = await asyncio.to_thread(
                self._generate_with_retry,
                prompt,
                self.json_config
            )
            
            # Parse JSON response
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                # Try to extract JSON from the response
                text = response.text.strip()
                if text.startswith("```json"):
                    text = text[7:]
                if text.endswith("```"):
                    text = text[:-3]
                return json.loads(text.strip())
                
        except Exception as e:
            logger.error(
                "llm_json_generation_failed",
                error=str(e),
                exc_info=True
            )
            # Return a default structure
            return {}
    
    def _generate_with_retry(self, prompt: str, config: GenerationConfig):
        """Generate content with retry logic"""
        return self.gemini_model.generate_content(
            prompt,
            generation_config=config,
            request_options={"retry": self.retry_config}
        )
    
    def _build_response_prompt(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext
    ) -> str:
        """Build prompt for response generation"""
        # Format code context
        code_context = self._format_code_context(code_chunks)
        
        # Format conversation history
        history_context = self._format_history(context.history[-3:])
        
        # Build intent-specific instructions
        intent_instructions = self._get_intent_instructions(intent)
        
        prompt = f"""You are an AI assistant helping developers understand their codebase.
You have deep knowledge of software development, design patterns, and best practices.

User Query: {query}
Query Intent: {intent.primary_intent.value}
Code Elements of Interest: {', '.join(intent.code_elements) if intent.code_elements else 'General'}

{intent_instructions}

Relevant Code Context:
{code_context}

{history_context}

Instructions for your response:
1. Directly address the user's query with accurate, helpful information
2. Reference specific code sections when relevant using the format: `filename:line_number`
3. Explain technical concepts clearly and concisely
4. If suggesting improvements or alternatives, explain why they would be beneficial
5. Be honest if the provided context doesn't fully answer the question
6. Keep your response focused and well-structured

Remember to:
- Use code blocks with language syntax highlighting
- Provide concrete examples when helpful
- Suggest logical next steps or related areas to explore
- Maintain a professional, helpful tone"""
        
        return prompt
    
    def _format_code_context(self, chunks: List[CodeChunk]) -> str:
        """Format code chunks for inclusion in prompt"""
        if not chunks:
            return "No relevant code context found."
        
        context_parts = []
        for i, chunk in enumerate(chunks[:5], 1):  # Limit to top 5 chunks
            context_parts.append(f"""
--- Code Section {i} ---
File: {chunk.file_path} (lines {chunk.start_line}-{chunk.end_line})
Language: {chunk.language}
Relevance Score: {chunk.combined_score:.2f}

```{chunk.language}
{chunk.content}
```""")
        
        return "\n".join(context_parts)
    
    def _format_history(self, history: List[Dict[str, Any]]) -> str:
        """Format conversation history"""
        if not history:
            return ""
        
        history_parts = ["Previous Conversation Context:"]
        for item in history:
            if item.get("query"):
                history_parts.append(f"User: {item['query']}")
            if item.get("answer"):
                history_parts.append(f"Assistant: {item['answer'][:200]}...")
        
        return "\n".join(history_parts) if len(history_parts) > 1 else ""
    
    def _get_intent_instructions(self, intent: IntentAnalysis) -> str:
        """Get intent-specific instructions"""
        instructions_map = {
            "explain": """Focus on providing a clear, educational explanation.
Break down complex concepts into understandable parts.
Use analogies or diagrams if helpful.""",
            
            "find": """Help locate the specific code or functionality requested.
Provide exact file paths and line numbers.
Mention related code that might also be relevant.""",
            
            "debug": """Analyze the code for potential issues or bugs.
Suggest specific fixes or debugging approaches.
Consider edge cases and error conditions.""",
            
            "refactor": """Suggest improvements to code structure and design.
Explain the benefits of proposed changes.
Consider maintainability and best practices.""",
            
            "analyze": """Provide a comprehensive analysis of the code.
Discuss patterns, architecture, and design decisions.
Identify strengths and potential improvements.""",
            
            "compare": """Compare and contrast different approaches or implementations.
Highlight trade-offs and use cases for each option.
Provide recommendations based on the context.""",
        }
        
        return instructions_map.get(
            intent.primary_intent.value,
            "Provide a helpful, accurate response to the query."
        )
    
    def _calculate_confidence(
        self,
        response: Any,
        chunks: List[CodeChunk],
        intent: IntentAnalysis
    ) -> float:
        """Calculate confidence score for the response"""
        base_confidence = 0.7
        
        # Boost for high-quality code chunks
        if chunks:
            avg_chunk_score = sum(c.combined_score for c in chunks[:3]) / min(3, len(chunks))
            base_confidence += 0.15 * avg_chunk_score
        
        # Boost for high intent confidence
        base_confidence += 0.1 * intent.confidence
        
        # Adjust based on response characteristics
        if response and hasattr(response, 'text'):
            # Penalize very short responses
            if len(response.text) < 100:
                base_confidence *= 0.8
            # Penalize responses that seem uncertain
            uncertainty_phrases = ["i'm not sure", "might be", "possibly", "unclear"]
            if any(phrase in response.text.lower() for phrase in uncertainty_phrases):
                base_confidence *= 0.9
        
        # Cap confidence at 0.95
        return min(base_confidence, 0.95)
    
    async def stream_response(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext
    ):
        """Stream response generation for real-time output"""
        prompt = self._build_response_prompt(query, intent, code_chunks, context)
        
        # Use streaming generation
        response_stream = await asyncio.to_thread(
            self.gemini_model.generate_content,
            prompt,
            generation_config=self.default_config,
            stream=True
        )
        
        # Yield chunks as they come
        for chunk in response_stream:
            if chunk.text:
                yield chunk.text