from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache
from typing import Optional


class Settings(BaseSettings):
    # Service Configuration
    PROJECT_NAME: str = "Query Intelligence Service"
    SERVICE_NAME: str = "query-intelligence"
    PORT: int = 8002
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "INFO"
    
    # External Service URLs
    REDIS_URL: str = "redis://localhost:6379"
    ANALYSIS_ENGINE_URL: str = "http://localhost:8001"
    PATTERN_MINING_URL: str = "http://localhost:8003"
    COLLABORATION_URL: str = "http://localhost:8004"
    
    # GCP Configuration
    GCP_PROJECT_ID: Optional[str] = None
    GCP_REGION: str = "us-central1"
    VERTEX_AI_PROJECT: Optional[str] = None
    VERTEX_AI_LOCATION: str = "us-central1"
    
    # Vertex AI / Gemini Configuration
    GEMINI_MODEL_NAME: str = "gemini-2.0-flash-exp"
    EMBEDDING_MODEL_NAME: str = "sentence-transformers/all-MiniLM-L6-v2"
    VERTEX_AI_ENDPOINT: Optional[str] = None
    
    # Pinecone Configuration
    PINECONE_API_KEY: Optional[str] = None
    PINECONE_INDEX_NAME: str = "ccl-code-embeddings"
    PINECONE_CLOUD: str = "aws"
    PINECONE_REGION: str = "us-west-2"
    
    # Cache Configuration
    CACHE_TTL_HOURS: int = 24
    CACHE_MAX_SIZE: int = 10000
    
    # Performance Configuration
    MAX_QUERY_LENGTH: int = 10000
    MAX_CODE_CHUNKS: int = 20
    MAX_RESPONSE_TOKENS: int = 2048
    QUERY_TIMEOUT_SECONDS: int = 30
    
    # Security Configuration
    JWT_SECRET_KEY: str = "a-very-secret-key"
    JWT_ALGORITHM: str = "HS256"
    API_KEY_HEADER: str = "X-API-Key"
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW_SECONDS: int = 60
    
    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090

    model_config = SettingsConfigDict(extra='ignore', env_file=".env")


@lru_cache()
def get_settings():
    return Settings()


# Keep backward compatibility
settings = get_settings()
