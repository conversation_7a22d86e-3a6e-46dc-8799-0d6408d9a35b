from contextlib import asynccontextmanager
from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from prometheus_fastapi_instrumentator import Instrumentator
import structlog

from .api import query, websocket
from .config.settings import get_settings
from .clients.redis import get_redis_client
from .clients.analysis_engine import get_analysis_engine_client
from .middleware.rate_limit import rate_limit_middleware

logger = structlog.get_logger()
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle"""
    # Startup
    logger.info("query_intelligence_starting", port=settings.PORT)

    # Initialize clients
    redis_client = get_redis_client()
    analysis_client = get_analysis_engine_client()

    # Test connections
    try:
        await redis_client.ping()
        logger.info("redis_connected")
    except Exception as e:
        logger.warning("redis_connection_failed", error=str(e))

    try:
        if await analysis_client.health_check():
            logger.info("analysis_engine_connected")
    except Exception as e:
        logger.warning("analysis_engine_connection_failed", error=str(e))

    yield

    # Shutdown
    logger.info("query_intelligence_shutting_down")
    await redis_client.aclose()
    await analysis_client.close()


# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    version="1.0.0",
    description="Natural language query processing for code understanding",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
app.middleware("http")(rate_limit_middleware)

# Add Prometheus instrumentation
if settings.ENABLE_METRICS:
    instrumentator = Instrumentator()
    instrumentator.instrument(app).expose(app, endpoint="/metrics")

# Include routers
app.include_router(query.router, prefix="/api/v1", tags=["queries"])
app.include_router(websocket.router, prefix="/api/v1", tags=["websocket"])


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    health_status = {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": "1.0.0",
        "checks": {},
    }

    # Check Redis
    try:
        redis_client = get_redis_client()
        await redis_client.ping()
        health_status["checks"]["redis"] = "ok"
    except Exception:
        health_status["checks"]["redis"] = "failed"
        health_status["status"] = "unhealthy"

    # Check Analysis Engine
    try:
        analysis_client = get_analysis_engine_client()
        if await analysis_client.health_check():
            health_status["checks"]["analysis_engine"] = "ok"
        else:
            health_status["checks"]["analysis_engine"] = "failed"
            health_status["status"] = "degraded"
    except Exception:
        health_status["checks"]["analysis_engine"] = "failed"
        health_status["status"] = "degraded"

    return health_status


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    # For now, if healthy then ready
    health = await health_check()
    if health["status"] == "healthy":
        return {"ready": True}
    return {"ready": False}, 503
