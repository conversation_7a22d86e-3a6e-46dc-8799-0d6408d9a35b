from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, Request
from ..models.query import QueryRequest, QueryResult
from ..services.query_processor import QueryProcessor, get_query_processor
from ..middleware.auth import get_optional_user
from ..middleware.rate_limit import create_rate_limit_dependency

router = APIRouter()

# Create endpoint-specific rate limiter
query_rate_limit = create_rate_limit_dependency(
    requests=50,  # 50 requests
    window=60,    # per minute
    key_prefix="query_endpoint"
)

@router.post("/query", response_model=QueryResult)
async def process_query(
    request: QueryRequest,
    req: Request,
    query_processor: QueryProcessor = Depends(get_query_processor),
    current_user: Optional[Dict[str, Any]] = Depends(get_optional_user),
    _: None = Depends(query_rate_limit)
):
    # Add user info to request if authenticated
    if current_user:
        request.user_id = current_user.get("user_id")
        
    # Store user in request state for rate limiting
    req.state.user = current_user
    
    return await query_processor.process_query(request)
