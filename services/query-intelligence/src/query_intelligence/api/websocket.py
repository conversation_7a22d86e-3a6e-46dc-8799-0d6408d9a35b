import json
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
import structlog

from ..models import QueryRequest, QueryStreamChunk, QueryIntent
from ..services import get_query_processor
from ..services.llm_service import LLMService

logger = structlog.get_logger()
router = APIRouter()


@router.websocket("/ws/query")
async def websocket_query_endpoint(
    websocket: WebSocket,
    query_processor = Depends(get_query_processor)
):
    """WebSocket endpoint for streaming query responses"""
    await websocket.accept()
    
    try:
        while True:
            # Receive query data
            data = await websocket.receive_text()
            
            try:
                # Parse the request
                request_data = json.loads(data)
                request = QueryRequest(**request_data)
                
                logger.info(
                    "websocket_query_received",
                    query=request.query,
                    repository_id=request.repository_id
                )
                
                # Send acknowledgment
                await websocket.send_json({
                    "type": "acknowledged",
                    "query": request.query
                })
                
                # Process the query with streaming
                await stream_query_response(websocket, request, query_processor)
                
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "message": "Invalid JSON format"
                })
            except Exception as e:
                logger.error(
                    "websocket_query_error",
                    error=str(e),
                    exc_info=True
                )
                await websocket.send_json({
                    "type": "error",
                    "message": f"Query processing error: {str(e)}"
                })
                
    except WebSocketDisconnect:
        logger.info("websocket_disconnected")
    except Exception as e:
        logger.error("websocket_error", error=str(e), exc_info=True)
        await websocket.close()


async def stream_query_response(
    websocket: WebSocket,
    request: QueryRequest,
    query_processor
):
    """Stream query response chunks over WebSocket"""
    
    try:
        # Start processing notification
        await websocket.send_json({
            "type": "processing_started",
            "message": "Analyzing your query..."
        })
        
        # Analyze intent
        context = query_processor._create_context(request)
        intent_analysis = await query_processor._analyze_intent(request.query, context)
        
        # Send intent analysis
        await websocket.send_json({
            "type": "intent_analyzed",
            "intent": intent_analysis.primary_intent.value,
            "confidence": intent_analysis.confidence
        })
        
        # Generate embedding and search
        await websocket.send_json({
            "type": "status",
            "message": "Searching codebase..."
        })
        
        query_embedding = await query_processor.semantic_search.generate_embedding(
            request.query,
            context_type="query"
        )
        
        search_results = await query_processor.semantic_search.search(
            embedding=query_embedding,
            repository_id=context.repository_id,
            filters=query_processor._build_search_filters(intent_analysis, context),
            limit=20
        )
        
        # Send search results summary
        await websocket.send_json({
            "type": "search_complete",
            "results_found": len(search_results.chunks),
            "search_time_ms": search_results.search_time_ms
        })
        
        # Rerank chunks
        ranked_chunks = await query_processor._rerank_chunks(
            request.query,
            intent_analysis,
            search_results.chunks
        )
        
        # Extract and send references
        references = query_processor._extract_references(ranked_chunks[:5])
        for ref in references:
            chunk = QueryStreamChunk(
                type="reference",
                reference=ref,
                done=False
            )
            await websocket.send_json(chunk.model_dump())
        
        # Stream the response generation
        await websocket.send_json({
            "type": "status",
            "message": "Generating response..."
        })
        
        # Use streaming response from LLM
        llm_service = query_processor.llm_service
        response_text = ""
        
        async for text_chunk in llm_service.stream_response(
            request.query,
            intent_analysis,
            ranked_chunks[:10],
            context
        ):
            response_text += text_chunk
            chunk = QueryStreamChunk(
                type="text",
                content=text_chunk,
                done=False
            )
            await websocket.send_json(chunk.model_dump())
        
        # Generate follow-up questions
        follow_ups = await query_processor._generate_follow_ups(
            request.query,
            type('obj', (object,), {'text': response_text})(),  # Mock response object
            context
        )
        
        # Send completion with metadata
        chunk = QueryStreamChunk(
            type="done",
            done=True,
            metadata={
                "intent": intent_analysis.primary_intent.value,
                "confidence": 0.85,  # Would be calculated properly
                "follow_up_questions": follow_ups,
                "total_chunks": len(search_results.chunks),
                "chunks_used": len(ranked_chunks[:10])
            }
        )
        await websocket.send_json(chunk.model_dump())
        
    except Exception as e:
        logger.error(
            "streaming_error",
            error=str(e),
            exc_info=True
        )
        await websocket.send_json({
            "type": "error",
            "message": f"Streaming error: {str(e)}"
        })