steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/query-intelligence:$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/query-intelligence:latest'
      - '-f'
      - 'services/query-intelligence/Dockerfile'
      - 'services/query-intelligence'
    id: 'build-image'

  # Push the Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '--all-tags'
      - 'gcr.io/$PROJECT_ID/query-intelligence'
    id: 'push-image'
    waitFor: ['build-image']

  # Deploy to Cloud Run (staging)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'query-intelligence-staging'
      - '--image'
      - 'gcr.io/$PROJECT_ID/query-intelligence:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '8Gi'
      - '--cpu'
      - '4'
      - '--min-instances'
      - '1'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - |
        ENVIRONMENT=staging,
        SERVICE_NAME=query-intelligence,
        PORT=8002,
        GCP_PROJECT_ID=$PROJECT_ID,
        GCP_REGION=us-central1,
        GEMINI_MODEL_NAME=gemini-2.0-flash-exp,
        REDIS_URL=redis://10.0.0.3:6379,
        ANALYSIS_ENGINE_URL=https://analysis-engine-staging-xxxxx.a.run.app,
        ENABLE_METRICS=true
      - '--set-secrets'
      - |
        JWT_SECRET_KEY=jwt-secret-key:latest,
        PINECONE_API_KEY=pinecone-api-key:latest,
        VERTEX_AI_PROJECT=vertex-ai-project:latest
    id: 'deploy-staging'
    waitFor: ['push-image']

  # Run integration tests against staging
  - name: 'gcr.io/$PROJECT_ID/query-intelligence:$COMMIT_SHA'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        export SERVICE_URL=$(gcloud run services describe query-intelligence-staging --platform managed --region us-central1 --format 'value(status.url)')
        poetry run pytest tests/integration/ --base-url=$SERVICE_URL
    id: 'integration-tests'
    waitFor: ['deploy-staging']

  # Deploy to Cloud Run (production) - only on main branch
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        if [[ "$BRANCH_NAME" == "main" ]]; then
          gcloud run deploy query-intelligence \
            --image gcr.io/$PROJECT_ID/query-intelligence:$COMMIT_SHA \
            --region us-central1 \
            --platform managed \
            --allow-unauthenticated \
            --memory 8Gi \
            --cpu 4 \
            --min-instances 2 \
            --max-instances 100 \
            --set-env-vars "ENVIRONMENT=production,SERVICE_NAME=query-intelligence,PORT=8002,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,GEMINI_MODEL_NAME=gemini-2.0-flash-exp,REDIS_URL=redis://********:6379,ANALYSIS_ENGINE_URL=https://analysis-engine-xxxxx.a.run.app,ENABLE_METRICS=true" \
            --set-secrets "JWT_SECRET_KEY=jwt-secret-key:latest,PINECONE_API_KEY=pinecone-api-key:latest,VERTEX_AI_PROJECT=vertex-ai-project:latest"
        else
          echo "Skipping production deployment (not on main branch)"
        fi
    id: 'deploy-production'
    waitFor: ['integration-tests']

# Substitutions
substitutions:
  _SERVICE_NAME: 'query-intelligence'

# Build configuration
options:
  machineType: 'E2_HIGHCPU_8'
  substitutionOption: 'ALLOW_LOOSE'
  logging: CLOUD_LOGGING_ONLY

# Timeout
timeout: '1200s'  # 20 minutes