import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
import os
from dotenv import load_dotenv

# Set test environment variables before loading .env
os.environ["ENVIRONMENT"] = "test"
os.environ["JWT_SECRET_KEY"] = "test-secret-key"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"
os.environ["ANALYSIS_ENGINE_URL"] = "http://localhost:8001"


@pytest.fixture(scope="session", autouse=True)
def load_env():
    load_dotenv(dotenv_path="/Users/<USER>/Documents/GitHub/episteme/.env")


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """Mock settings for testing"""
    from query_intelligence.config.settings import Settings

    settings = Settings(
        SERVICE_NAME="query-intelligence-test",
        ENVIRONMENT="test",
        JWT_SECRET_KEY="test-secret-key",
        REDIS_URL="redis://localhost:6379/1",
        ANALYSIS_ENGINE_URL="http://localhost:8001",
        PINECONE_API_KEY=None,  # Disabled for tests
        RATE_LIMIT_REQUESTS=100,
        RATE_LIMIT_WINDOW_SECONDS=60,
        CACHE_TTL_HOURS=1,
        GEMINI_MODEL_NAME="gemini-2.0-flash-test",
        GCP_PROJECT_ID="test-project",
        GCP_REGION="us-central1",
    )

    with patch(
        "query_intelligence.config.settings.get_settings", return_value=settings
    ):
        yield settings


@pytest.fixture
async def mock_redis():
    """Mock Redis client for testing"""
    mock = AsyncMock()
    mock.get = AsyncMock(return_value=None)
    mock.set = AsyncMock()
    mock.ping = AsyncMock()
    mock.aclose = AsyncMock()
    mock.pipeline = Mock()

    # Setup pipeline mock
    pipeline = Mock()
    pipeline.zremrangebyscore = Mock()
    pipeline.zcard = Mock()
    pipeline.zadd = Mock()
    pipeline.expire = Mock()
    pipeline.execute = AsyncMock(return_value=[None, 0, None, None])
    mock.pipeline.return_value = pipeline

    with patch("query_intelligence.clients.redis.get_redis_client", return_value=mock):
        yield mock


@pytest.fixture
def mock_structlog():
    """Mock structured logging"""
    logger = Mock()
    logger.info = Mock()
    logger.error = Mock()
    logger.warning = Mock()
    logger.debug = Mock()

    with patch("structlog.get_logger", return_value=logger):
        yield logger
