import pytest
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock, <PERSON><PERSON>
import json

from query_intelligence.main import app
from query_intelligence.models import QueryResult, QueryIntent


@pytest.fixture
async def client():
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def mock_query_processor():
    mock = AsyncMock()
    mock.process_query = AsyncMock()
    return mock


class TestQueryAPI:
    
    @pytest.mark.asyncio
    async def test_health_check(self, client):
        response = await client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] in ["healthy", "degraded", "unhealthy"]
        assert "service" in data
        assert "version" in data
        
    @pytest.mark.asyncio
    async def test_ready_check(self, client):
        response = await client.get("/ready")
        assert response.status_code in [200, 503]
        data = response.json()
        assert "ready" in data
        
    @pytest.mark.asyncio
    async def test_process_query_success(self, client, mock_query_processor):
        # Setup mock
        mock_result = QueryResult(
            answer="Authentication uses JWT tokens validated in middleware",
            intent=QueryIntent.EXPLAIN,
            confidence=0.92,
            references=[],
            execution_time_ms=85.5,
            follow_up_questions=["How are tokens generated?"],
            metadata={"model_used": "gemini-2.0"}
        )
        mock_query_processor.process_query.return_value = mock_result
        
        with patch('query_intelligence.api.query.get_query_processor', return_value=mock_query_processor):
            # Make request
            response = await client.post(
                "/api/v1/query",
                json={
                    "query": "How does authentication work?",
                    "repository_id": "test-repo"
                }
            )
            
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["answer"] == mock_result.answer
        assert data["intent"] == "explain"
        assert data["confidence"] == 0.92
        assert len(data["follow_up_questions"]) == 1
        
    @pytest.mark.asyncio
    async def test_process_query_with_auth(self, client, mock_query_processor):
        # Setup mock
        mock_result = QueryResult(
            answer="Test answer",
            intent=QueryIntent.FIND,
            confidence=0.85,
            references=[],
            execution_time_ms=50.0,
            follow_up_questions=[],
            metadata={}
        )
        mock_query_processor.process_query.return_value = mock_result
        
        # Mock JWT auth
        with patch('query_intelligence.api.query.get_query_processor', return_value=mock_query_processor):
            with patch('query_intelligence.middleware.auth.jwt_auth.verify_token', return_value={
                "sub": "user-123",
                "email": "<EMAIL>",
                "roles": ["user"],
                "exp": 9999999999
            }):
                # Make request with auth header
                response = await client.post(
                    "/api/v1/query",
                    json={
                        "query": "Find authentication code",
                        "repository_id": "test-repo"
                    },
                    headers={"Authorization": "Bearer fake-jwt-token"}
                )
                
        # Verify
        assert response.status_code == 200
        # Verify user_id was set in request
        call_args = mock_query_processor.process_query.call_args[0][0]
        assert call_args.user_id == "user-123"
        
    @pytest.mark.asyncio
    async def test_process_query_validation_error(self, client):
        # Missing required field
        response = await client.post(
            "/api/v1/query",
            json={"query": "Test query"}  # Missing repository_id
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
        
    @pytest.mark.asyncio
    async def test_process_query_rate_limit(self, client):
        # Mock rate limiter to simulate limit exceeded
        with patch('query_intelligence.middleware.rate_limit.RateLimiter.is_allowed', 
                   return_value=(False, {"limit": 50, "remaining": 0, "reset": 1234567890})):
            
            response = await client.post(
                "/api/v1/query",
                json={
                    "query": "Test query",
                    "repository_id": "test-repo"
                }
            )
            
        assert response.status_code == 429
        assert "Rate limit exceeded" in response.json()["detail"]
        assert response.headers["X-RateLimit-Limit"] == "50"
        assert response.headers["X-RateLimit-Remaining"] == "0"
        
    @pytest.mark.asyncio
    async def test_process_query_with_filters(self, client, mock_query_processor):
        # Setup
        mock_query_processor.process_query.return_value = QueryResult(
            answer="Found in specific files",
            intent=QueryIntent.FIND,
            confidence=0.9,
            references=[],
            execution_time_ms=75.0,
            follow_up_questions=[],
            metadata={}
        )
        
        with patch('query_intelligence.api.query.get_query_processor', return_value=mock_query_processor):
            # Request with filters
            response = await client.post(
                "/api/v1/query",
                json={
                    "query": "Find error handling",
                    "repository_id": "test-repo",
                    "filters": {
                        "file_pattern": "*.py",
                        "exclude": ["tests/"]
                    }
                }
            )
            
        assert response.status_code == 200
        # Verify filters were passed
        call_args = mock_query_processor.process_query.call_args[0][0]
        assert call_args.filters["file_pattern"] == "*.py"
        
    @pytest.mark.asyncio
    async def test_metrics_endpoint(self, client):
        response = await client.get("/metrics")
        assert response.status_code == 200
        # Prometheus metrics are in text format
        assert "query_intelligence_queries_total" in response.text