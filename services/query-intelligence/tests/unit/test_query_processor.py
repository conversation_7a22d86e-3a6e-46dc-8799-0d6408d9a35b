import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import numpy as np
from datetime import timed<PERSON><PERSON>

from query_intelligence.models import (
    QueryRequest,
    QueryResult,
    QueryIntent,
    IntentAnalysis,
    CodeChunk,
    SearchResult,
    GeneratedResponse,
    CodeReference,
)
from query_intelligence.services.query_processor import QueryProcessor


@pytest.fixture
def mock_semantic_search():
    mock = Mock()
    mock.generate_embedding = AsyncMock(return_value=np.array([0.1, 0.2, 0.3]))
    mock.search = AsyncMock()
    return mock


@pytest.fixture
def mock_llm_service():
    mock = Mock()
    mock.generate_response = AsyncMock()
    mock.generate_json_response = AsyncMock()
    return mock


@pytest.fixture
def mock_redis_client():
    mock = Mock()
    mock.get = AsyncMock(return_value=None)
    mock.set = AsyncMock()
    return mock


@pytest.fixture
def query_processor(mock_semantic_search, mock_llm_service, mock_redis_client):
    with patch('query_intelligence.services.query_processor.SemanticSearchService', return_value=mock_semantic_search):
        with patch('query_intelligence.services.query_processor.LLMService', return_value=mock_llm_service):
            with patch('query_intelligence.services.query_processor.get_redis_client', return_value=mock_redis_client):
                processor = QueryProcessor()
                return processor


@pytest.fixture
def sample_request():
    return QueryRequest(
        query="How does authentication work?",
        repository_id="test-repo",
        user_id="test-user",
        session_id="test-session"
    )


@pytest.fixture
def sample_code_chunks():
    return [
        CodeChunk(
            file_path="src/auth/middleware.py",
            start_line=10,
            end_line=50,
            content="def authenticate(request):\\n    # JWT validation logic",
            language="python",
            similarity_score=0.9,
            recency_score=0.8,
            combined_score=0.85
        ),
        CodeChunk(
            file_path="src/auth/utils.py",
            start_line=5,
            end_line=25,
            content="def validate_token(token):\\n    # Token validation",
            language="python",
            similarity_score=0.8,
            recency_score=0.7,
            combined_score=0.75
        )
    ]


class TestQueryProcessor:
    
    @pytest.mark.asyncio
    async def test_process_query_success(
        self,
        query_processor,
        sample_request,
        sample_code_chunks,
        mock_semantic_search,
        mock_llm_service
    ):
        # Setup mocks
        mock_llm_service.generate_json_response.return_value = {
            "primary_intent": "explain",
            "code_elements": ["authentication", "middleware"],
            "scope": "repository",
            "context_depth": "normal",
            "confidence": 0.9
        }
        
        mock_semantic_search.search.return_value = SearchResult(
            chunks=sample_code_chunks,
            total_results=2,
            search_time_ms=50.0
        )
        
        mock_llm_service.generate_response.return_value = GeneratedResponse(
            text="Authentication works by validating JWT tokens...",
            confidence=0.92,
            model_used="gemini-2.0-flash",
            prompt_tokens=100,
            completion_tokens=50
        )
        
        mock_llm_service.generate_json_response.side_effect = [
            {
                "primary_intent": "explain",
                "code_elements": ["authentication"],
                "scope": "repository",
                "context_depth": "normal",
                "confidence": 0.9
            },
            ["How are tokens validated?", "What happens on auth failure?", "How to refresh tokens?"]
        ]
        
        # Execute
        result = await query_processor.process_query(sample_request)
        
        # Verify
        assert isinstance(result, QueryResult)
        assert result.answer == "Authentication works by validating JWT tokens..."
        assert result.intent == QueryIntent.EXPLAIN
        assert result.confidence == 0.92
        assert len(result.references) == 2
        assert result.references[0].file_path == "src/auth/middleware.py"
        assert len(result.follow_up_questions) == 3
        
    @pytest.mark.asyncio
    async def test_process_query_with_cache_hit(
        self,
        query_processor,
        sample_request,
        mock_redis_client
    ):
        # Setup cache hit
        cached_result = {
            "answer": "Cached answer",
            "intent": "explain",
            "confidence": 0.9,
            "references": [],
            "execution_time_ms": 50.0,
            "follow_up_questions": [],
            "metadata": {}
        }
        mock_redis_client.get.return_value = '{"answer": "Cached answer", "intent": "explain", "confidence": 0.9, "references": [], "execution_time_ms": 50.0, "follow_up_questions": [], "metadata": {}}'
        
        # Execute
        result = await query_processor.process_query(sample_request)
        
        # Verify cache was used
        assert result.answer == "Cached answer"
        mock_redis_client.get.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_analyze_intent(self, query_processor, mock_llm_service):
        # Setup
        mock_llm_service.generate_json_response.return_value = {
            "primary_intent": "debug",
            "code_elements": ["error", "exception"],
            "scope": "file",
            "context_depth": "deep",
            "confidence": 0.85
        }
        
        # Execute
        from query_intelligence.models import QueryContext
        context = QueryContext(
            repository_id="test-repo",
            user_id="test-user"
        )
        intent = await query_processor._analyze_intent("Why is this throwing an error?", context)
        
        # Verify
        assert intent.primary_intent == QueryIntent.DEBUG
        assert intent.code_elements == ["error", "exception"]
        assert intent.scope == "file"
        assert intent.confidence == 0.85
        
    @pytest.mark.asyncio
    async def test_rerank_chunks(self, query_processor, sample_code_chunks):
        # Setup
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["authentication"],
            scope="repository",
            context_depth="normal",
            confidence=0.9
        )
        
        # Execute
        ranked_chunks = await query_processor._rerank_chunks(
            "How does authentication work?",
            intent,
            sample_code_chunks
        )
        
        # Verify
        assert len(ranked_chunks) == 2
        assert all(hasattr(chunk, 'combined_score') for chunk in ranked_chunks)
        assert ranked_chunks[0].combined_score >= ranked_chunks[1].combined_score
        
    def test_extract_references(self, query_processor, sample_code_chunks):
        # Execute
        references = query_processor._extract_references(sample_code_chunks)
        
        # Verify
        assert len(references) == 2
        assert all(isinstance(ref, CodeReference) for ref in references)
        assert references[0].file_path == "src/auth/middleware.py"
        assert references[0].relevance_score == 0.85
        
    def test_format_snippet(self, query_processor):
        # Test short snippet
        short_content = "line1\\nline2\\nline3"
        result = query_processor._format_snippet(short_content)
        assert result == short_content
        
        # Test long snippet
        long_content = "\\n".join([f"line{i}" for i in range(20)])
        result = query_processor._format_snippet(long_content, max_lines=10)
        assert "..." in result
        assert "line0" in result
        assert "line19" in result
        
    def test_generate_cache_key(self, query_processor, sample_request):
        # Execute
        key = query_processor._generate_cache_key(sample_request)
        
        # Verify
        assert key.startswith("query:v1:")
        assert len(key) > 10
        
        # Test consistency
        key2 = query_processor._generate_cache_key(sample_request)
        assert key == key2
        
    def test_extract_chunk_features(self, query_processor, sample_code_chunks):
        # Test for EXPLAIN intent
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["authenticate"],
            scope="repository",
            context_depth="normal",
            confidence=0.9
        )
        
        features = query_processor._extract_chunk_features(sample_code_chunks[0], intent)
        assert isinstance(features, np.ndarray)
        assert len(features) == 6
        
        # Test for DEBUG intent
        intent.primary_intent = QueryIntent.DEBUG
        features = query_processor._extract_chunk_features(sample_code_chunks[0], intent)
        assert isinstance(features, np.ndarray)
        assert len(features) == 6