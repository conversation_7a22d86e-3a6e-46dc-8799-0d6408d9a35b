import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import numpy as np

from query_intelligence.services.semantic_search import SemanticSearchService
from query_intelligence.models import CodeChunk, SearchResult, EmbeddingVector


@pytest.fixture
def mock_sentence_transformer():
    mock = MagicMock()
    mock.encode.return_value = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
    mock.get_sentence_embedding_dimension.return_value = 5
    return mock


@pytest.fixture
def mock_pinecone_index():
    mock = Mock()
    mock.query = Mock()
    mock.upsert = Mock()
    return mock


@pytest.fixture
def mock_analysis_engine_client():
    mock = Mock()
    mock.search_embeddings = AsyncMock()
    return mock


@pytest.fixture
def semantic_search_service(
    mock_sentence_transformer,
    mock_pinecone_index,
    mock_analysis_engine_client
):
    with patch('query_intelligence.services.semantic_search.SentenceTransformer', return_value=mock_sentence_transformer):
        with patch('query_intelligence.services.semantic_search.get_analysis_engine_client', return_value=mock_analysis_engine_client):
            service = SemanticSearchService()
            service.index = mock_pinecone_index
            return service


@pytest.fixture
def sample_code_chunks():
    return [
        CodeChunk(
            file_path="src/main.py",
            start_line=1,
            end_line=10,
            content="def main():\\n    print('Hello')",
            language="python",
            similarity_score=0.9,
            recency_score=0.8
        ),
        CodeChunk(
            file_path="src/utils.py",
            start_line=5,
            end_line=15,
            content="def helper():\\n    return True",
            language="python",
            similarity_score=0.7,
            recency_score=0.6
        )
    ]


class TestSemanticSearchService:
    
    @pytest.mark.asyncio
    async def test_generate_embedding_for_code(
        self,
        semantic_search_service,
        mock_sentence_transformer
    ):
        # Execute
        embedding = await semantic_search_service.generate_embedding(
            "def test(): pass",
            context_type="code"
        )
        
        # Verify
        assert isinstance(embedding, np.ndarray)
        assert embedding.shape == (5,)
        mock_sentence_transformer.encode.assert_called_once()
        call_args = mock_sentence_transformer.encode.call_args[0][0]
        assert call_args.startswith("[CODE]")
        
    @pytest.mark.asyncio
    async def test_generate_embedding_for_query(
        self,
        semantic_search_service,
        mock_sentence_transformer
    ):
        # Execute
        embedding = await semantic_search_service.generate_embedding(
            "How does authentication work?",
            context_type="query"
        )
        
        # Verify
        assert isinstance(embedding, np.ndarray)
        call_args = mock_sentence_transformer.encode.call_args[0][0]
        assert call_args.startswith("[QUERY]")
        
    @pytest.mark.asyncio
    async def test_generate_embedding_with_cache(
        self,
        semantic_search_service,
        mock_sentence_transformer
    ):
        # First call
        text = "test code"
        embedding1 = await semantic_search_service.generate_embedding(text, "code")
        
        # Second call (should use cache)
        embedding2 = await semantic_search_service.generate_embedding(text, "code")
        
        # Verify
        assert np.array_equal(embedding1, embedding2)
        # Should only be called once due to caching
        assert mock_sentence_transformer.encode.call_count == 1
        
    @pytest.mark.asyncio
    async def test_search_with_pinecone(
        self,
        semantic_search_service,
        mock_pinecone_index
    ):
        # Setup
        mock_pinecone_index.query.return_value = MagicMock(
            matches=[
                MagicMock(
                    score=0.95,
                    metadata={
                        "file_path": "src/auth.py",
                        "start_line": 10,
                        "end_line": 20,
                        "content": "auth code",
                        "language": "python",
                        "recency_score": 0.8
                    }
                ),
                MagicMock(
                    score=0.85,
                    metadata={
                        "file_path": "src/user.py",
                        "start_line": 5,
                        "end_line": 15,
                        "content": "user code",
                        "language": "python",
                        "recency_score": 0.7
                    }
                )
            ]
        )
        
        # Execute
        embedding = np.array([0.1, 0.2, 0.3])
        result = await semantic_search_service.search(
            embedding=embedding,
            repository_id="test-repo",
            filters={"language": "python"},
            limit=10
        )
        
        # Verify
        assert isinstance(result, SearchResult)
        assert result.total_results == 2
        assert len(result.chunks) == 2
        assert result.chunks[0].file_path == "src/auth.py"
        assert result.chunks[0].similarity_score == 0.95
        
        # Verify Pinecone was called correctly
        mock_pinecone_index.query.assert_called_once()
        call_kwargs = mock_pinecone_index.query.call_args[1]
        assert call_kwargs["filter"]["repository_id"] == "test-repo"
        assert call_kwargs["top_k"] == 10
        
    @pytest.mark.asyncio
    async def test_search_fallback_to_analysis_engine(
        self,
        semantic_search_service,
        mock_analysis_engine_client
    ):
        # Disable Pinecone
        semantic_search_service.index = None
        
        # Setup mock response
        mock_analysis_engine_client.search_embeddings.return_value = {
            "results": [
                {
                    "file_path": "src/main.py",
                    "start_line": 1,
                    "end_line": 10,
                    "content": "main code",
                    "language": "python",
                    "similarity_score": 0.9,
                    "recency_score": 0.8
                }
            ]
        }
        
        # Execute
        embedding = np.array([0.1, 0.2, 0.3])
        result = await semantic_search_service.search(
            embedding=embedding,
            repository_id="test-repo",
            limit=10
        )
        
        # Verify
        assert result.total_results == 1
        assert result.chunks[0].file_path == "src/main.py"
        mock_analysis_engine_client.search_embeddings.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_search_error_handling(
        self,
        semantic_search_service,
        mock_pinecone_index
    ):
        # Setup error
        mock_pinecone_index.query.side_effect = Exception("Connection error")
        
        # Execute
        embedding = np.array([0.1, 0.2, 0.3])
        result = await semantic_search_service.search(
            embedding=embedding,
            repository_id="test-repo"
        )
        
        # Verify graceful failure
        assert result.total_results == 0
        assert len(result.chunks) == 0
        assert result.search_time_ms > 0
        
    @pytest.mark.asyncio
    async def test_index_code_chunk(
        self,
        semantic_search_service,
        mock_pinecone_index,
        mock_sentence_transformer
    ):
        # Setup
        chunk = CodeChunk(
            file_path="src/new.py",
            start_line=1,
            end_line=10,
            content="new code content",
            language="python",
            similarity_score=0.0,
            recency_score=1.0
        )
        
        # Execute
        success = await semantic_search_service.index_code_chunk(
            chunk=chunk,
            repository_id="test-repo"
        )
        
        # Verify
        assert success is True
        mock_pinecone_index.upsert.assert_called_once()
        vectors = mock_pinecone_index.upsert.call_args[1]["vectors"]
        assert len(vectors) == 1
        assert vectors[0][0] == "test-repo:src/new.py:1"
        
    @pytest.mark.asyncio
    async def test_batch_index_chunks(
        self,
        semantic_search_service,
        mock_pinecone_index,
        sample_code_chunks
    ):
        # Execute
        indexed_count = await semantic_search_service.batch_index_chunks(
            chunks=sample_code_chunks,
            repository_id="test-repo",
            batch_size=2
        )
        
        # Verify
        assert indexed_count == 2
        mock_pinecone_index.upsert.assert_called_once()
        vectors = mock_pinecone_index.upsert.call_args[1]["vectors"]
        assert len(vectors) == 2