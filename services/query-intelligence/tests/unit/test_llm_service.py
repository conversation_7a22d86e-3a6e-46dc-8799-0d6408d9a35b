import pytest
from unittest.mock import Mock, patch, MagicMock
import json

from query_intelligence.services.llm_service import LLMService
from query_intelligence.models import (
    QueryContext,
    IntentAnalysis,
    CodeChunk,
    GeneratedResponse,
    QueryIntent,
)


@pytest.fixture
def mock_generative_model():
    mock = MagicMock()
    mock.generate_content = Mock()
    return mock


@pytest.fixture
def mock_vertexai():
    with patch("query_intelligence.services.llm_service.vertexai") as mock:
        yield mock


@pytest.fixture
def llm_service(mock_generative_model, mock_vertexai):
    with patch(
        "query_intelligence.services.llm_service.GenerativeModel",
        return_value=mock_generative_model,
    ):
        with patch(
            "query_intelligence.services.llm_service.default",
            return_value=(None, "test-project"),
        ):
            service = LLMService()
            return service


@pytest.fixture
def sample_intent():
    return IntentAnalysis(
        primary_intent=QueryIntent.EXPLAIN,
        code_elements=["authentication", "middleware"],
        scope="repository",
        context_depth="normal",
        confidence=0.9,
    )


@pytest.fixture
def sample_code_chunks():
    return [
        CodeChunk(
            file_path="src/auth/middleware.py",
            start_line=10,
            end_line=50,
            content="def authenticate(request):\\n    # JWT validation logic\\n    token = request.headers.get('Authorization')\\n    if not token:\\n        raise AuthError('No token provided')",
            language="python",
            similarity_score=0.9,
            recency_score=0.8,
            combined_score=0.85,
        ),
        CodeChunk(
            file_path="src/auth/utils.py",
            start_line=5,
            end_line=25,
            content="def validate_token(token):\\n    # Decode and verify JWT\\n    try:\\n        payload = jwt.decode(token, SECRET_KEY)\\n        return payload\\n    except jwt.InvalidTokenError:\\n        return None",
            language="python",
            similarity_score=0.8,
            recency_score=0.7,
            combined_score=0.75,
        ),
    ]


@pytest.fixture
def sample_context():
    return QueryContext(
        repository_id="test-repo",
        user_id="test-user",
        session_id="test-session",
        history=[
            {"query": "What is JWT?", "answer": "JWT is JSON Web Token..."},
            {"query": "How to validate JWT?", "answer": "To validate JWT..."},
        ],
    )


class TestLLMService:

    @pytest.mark.asyncio
    async def test_generate_response_success(
        self,
        llm_service,
        mock_generative_model,
        sample_intent,
        sample_code_chunks,
        sample_context,
    ):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.text = "Authentication in this codebase uses JWT tokens. The middleware validates tokens on each request..."
        mock_response.usage_metadata.prompt_token_count = 500
        mock_response.usage_metadata.candidates_token_count = 150
        mock_generative_model.generate_content.return_value = mock_response

        # Execute
        response = await llm_service.generate_response(
            query="How does authentication work?",
            intent=sample_intent,
            code_chunks=sample_code_chunks,
            context=sample_context,
        )

        # Verify
        assert isinstance(response, GeneratedResponse)
        assert response.text == mock_response.text
        assert response.confidence > 0.7
        assert response.prompt_tokens == 500
        assert response.completion_tokens == 150
        assert response.generation_time_ms > 0

        # Verify prompt was built correctly
        mock_generative_model.generate_content.assert_called_once()
        prompt = mock_generative_model.generate_content.call_args[0][0]
        assert "How does authentication work?" in prompt
        assert "src/auth/middleware.py" in prompt
        assert "JWT validation logic" in prompt

    @pytest.mark.asyncio
    async def test_generate_json_response_success(
        self, llm_service, mock_generative_model
    ):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.text = json.dumps(
            {"intent": "explain", "confidence": 0.9, "elements": ["auth", "jwt"]}
        )
        mock_generative_model.generate_content.return_value = mock_response

        # Execute
        result = await llm_service.generate_json_response(
            "Analyze this query and return JSON"
        )

        # Verify
        assert isinstance(result, dict)
        assert result["intent"] == "explain"
        assert result["confidence"] == 0.9
        assert result["elements"] == ["auth", "jwt"]

    @pytest.mark.asyncio
    async def test_generate_json_response_with_markdown(
        self, llm_service, mock_generative_model
    ):
        # Setup response with markdown code block
        mock_response = MagicMock()
        mock_response.text = '```json\n{"key": "value", "number": 42}\n```'
        mock_generative_model.generate_content.return_value = mock_response

        # Execute
        result = await llm_service.generate_json_response("Generate JSON")

        # Verify JSON was extracted correctly
        assert result == {"key": "value", "number": 42}

    @pytest.mark.asyncio
    async def test_generate_json_response_error_handling(
        self, llm_service, mock_generative_model
    ):
        # Setup invalid JSON response
        mock_response = MagicMock()
        mock_response.text = "This is not valid JSON"
        mock_generative_model.generate_content.return_value = mock_response

        # Execute
        result = await llm_service.generate_json_response("Generate JSON")

        # Verify it returns empty dict on error
        assert result == {}

    def test_build_response_prompt(
        self, llm_service, sample_intent, sample_code_chunks, sample_context
    ):
        # Execute
        prompt = llm_service._build_response_prompt(
            query="How does authentication work?",
            intent=sample_intent,
            code_chunks=sample_code_chunks,
            context=sample_context,
        )

        # Verify prompt structure
        assert "How does authentication work?" in prompt
        assert "explain" in prompt.lower()
        assert "authentication, middleware" in prompt
        assert "src/auth/middleware.py" in prompt
        assert "JWT validation logic" in prompt
        assert "Previous Conversation Context:" in prompt
        assert "What is JWT?" in prompt

    def test_format_code_context(self, llm_service, sample_code_chunks):
        # Execute
        context = llm_service._format_code_context(sample_code_chunks)

        # Verify
        assert "Code Section 1" in context
        assert "Code Section 2" in context
        assert "src/auth/middleware.py (lines 10-50)" in context
        assert "```python" in context
        assert "def authenticate(request):" in context

    def test_format_code_context_empty(self, llm_service):
        # Execute
        context = llm_service._format_code_context([])

        # Verify
        assert context == "No relevant code context found."

    def test_format_history(self, llm_service):
        # Test with history
        history = [
            {"query": "What is JWT?", "answer": "JWT stands for JSON Web Token..."},
            {
                "query": "How to validate?",
                "answer": "To validate a JWT token, you need to verify the signature...",
            },
        ]

        result = llm_service._format_history(history)

        assert "Previous Conversation Context:" in result
        assert "User: What is JWT?" in result
        assert "Assistant: JWT stands for" in result

    def test_format_history_empty(self, llm_service):
        # Test with empty history
        result = llm_service._format_history([])
        assert result == ""

    def test_get_intent_instructions(self, llm_service):
        # Test each intent type
        for intent in QueryIntent:
            if intent != QueryIntent.UNKNOWN:
                instructions = llm_service._get_intent_instructions(
                    IntentAnalysis(
                        primary_intent=intent,
                        code_elements=[],
                        scope="repository",
                        context_depth="normal",
                        confidence=0.9,
                    )
                )
                assert len(instructions) > 0
                assert isinstance(instructions, str)

    def test_calculate_confidence(self, llm_service, sample_code_chunks):
        # Test with high-quality chunks
        mock_response = MagicMock()
        mock_response.text = (
            "This is a detailed explanation of the authentication system..."
        )

        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=[],
            scope="repository",
            context_depth="normal",
            confidence=0.9,
        )

        confidence = llm_service._calculate_confidence(
            mock_response, sample_code_chunks, intent
        )

        assert 0.7 <= confidence <= 0.95

    def test_calculate_confidence_short_response(self, llm_service):
        # Test with very short response
        mock_response = MagicMock()
        mock_response.text = "Yes"

        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=[],
            scope="repository",
            context_depth="normal",
            confidence=0.5,
        )

        confidence = llm_service._calculate_confidence(mock_response, [], intent)

        assert confidence < 0.7  # Should be penalized for short response
